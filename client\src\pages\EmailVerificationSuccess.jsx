import React, { useState, useEffect } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { toast } from "sonner";
import { Mail } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

export default function EmailVerificationSuccess() {
  const navigate = useNavigate();
  const location = useLocation();

  // Get email from navigation state
  const email = location.state?.email;

  // State for resend functionality
  const [isResending, setIsResending] = useState(false);
  const [resendTimer, setResendTimer] = useState(60); // 60 seconds countdown
  const [canResend, setCanResend] = useState(false);

  // Timer countdown for resend button
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [resendTimer]);

  const handleResendEmail = async () => {
  if (!email) {
    toast.error("Email address not found. Please try registering again.");
    return;
  }

  if (!canResend || resendTimer > 0) return;

  setIsResending(true);

  try {
    // Actual API call
    await axios.post("http://localhost:3000/api/auth/resend-verification", {
      email,
    });

    toast.success("Verification email sent again!");

    // Reset resend timer and state
    setResendTimer(60); // 60 seconds cooldown
    setCanResend(false);
  } catch (error) {
    console.error(error);
    const message =
      error?.response?.data?.message ||
      "Failed to resend verification email. Please try again.";
    toast.error(message);
  } finally {
    setIsResending(false);
  }
};

  const openMailApp = () => {
    // Try to open default mail app
    window.location.href = "mailto:";
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <div className="w-full max-w-md bg-card-light dark:bg-card-dark rounded-2xl shadow-xl border relative overflow-hidden">
        {/* Decorative Header */}
        <div className="h-24 bg-muted/60 dark:bg-muted-dark/60 rounded-t-2xl"></div>

        {/* Floating Icon */}
        <div className="absolute top-14 left-1/2 transform -translate-x-1/2 bg-muted dark:bg-muted-dark p-4 rounded-full border-4 border-card-light dark:border-card-dark shadow-lg">
          <Mail className="text-primary w-8 h-8" />
        </div>

        {/* Content */}
        <div className="mt-12 px-8 pb-8 text-center">
          <h1 className="text-3xl font-bold text-foreground dark:text-foreground-dark">
            Check Your Inbox
          </h1>
          <p className="mt-2 text-sm text-muted-foreground dark:text-muted-foreground-dark">
            We’ve just sent an email to{" "}
            <span className="font-medium text-foreground dark:text-foreground-dark">
              {email || "<EMAIL>"}
            </span>
            . Tap the link in the email to verify your account.
          </p>
          <Button
            variant="outline"
            onClick={openMailApp}
            className="mt-6 py-3 transition-colors duration-200 hover:bg-primary-dark"
          >
            Open Mail App
          </Button>
        </div>

        <Separator/>

        {/* Resend Section */}
        <div className="p-6 flex flex-col items-center justify-center space-y-2">
          <p className="text-sm text-muted-foreground dark:text-muted-foreground-dark">
            Didn’t receive the email?
          </p>
          <Link
            to="#"
            onClick={
              resendTimer > 0 || isResending ? undefined : handleResendEmail
            }
            className={cn(
              "text-sm font-medium transition-opacity duration-200",
              resendTimer > 0 || isResending
                ? "pointer-events-none opacity-50 cursor-not-allowed"
                : "text-primary hover:underline"
            )}
          >
            {isResending
              ? "Sending..."
              : `Resend${resendTimer > 0 ? ` in ${resendTimer}s` : ""}`}
          </Link>
        </div>
      </div>
    </div>
  );
}
