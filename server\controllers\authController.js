const bcrypt = require("bcrypt");
const crypto = require("crypto");
const User = require("../models/User");
const { sendEmail } = require("../utils/mailer");

exports.registerUser = async (req, res) => {
  try {
    const { username, email, password } = req.body;

    if (!username || !email || !password) {
      return res
        .status(400)
        .json({ success: false, message: "All fields are required" });
    }

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res
        .status(409)
        .json({ success: false, message: "Email or username already exists" });
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    const emailVerifyToken = crypto.randomBytes(32).toString("hex");
    const emailVerifyExpires = Date.now() + 60 * 60 * 1000; // 1 hour

    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      emailVerifyToken,
      emailVerifyExpires,
    });

    await newUser.save();

    // ✅ Construct email verification URL
    const verifyUrl = `${process.env.BASE_URL}/verify-email/${newUser._id}/${emailVerifyToken}`;

    // ✅ Email content
    const html = `
      <h3>Welcome to GigGlobe!</h3>
      <p>Click the link below to verify your email:</p>
      <a href="${verifyUrl}" target="_blank">Verify Email</a>
      <p>This link will expire in 1 hour.</p>
    `;

    await sendEmail(email, "Verify Your Email - GigGlobe", html);

    return res.status(201).json({
      success: true,
      message:
        "User registered successfully. A verification email has been sent.",
    });
  } catch (err) {
    console.error("Register error:", err);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

exports.resendVerificationEmail = async (req, res) => {
  const { email } = req.body

  if (!email) {
    return res.status(400).json({ success: false, message: "Email is required" })
  }

  try {
    const user = await User.findOne({ email })

    if (!user) {
      return res.status(404).json({ success: false, message: "User not found" })
    }

    if (user.isVerified) {
      return res.status(400).json({ success: false, message: "Account already verified" })
    }

    // Generate new token and expiry
    const token = crypto.randomBytes(32).toString("hex")
    const expiry = Date.now() + 60 * 60 * 1000 // 1 hour

    user.emailVerifyToken = token
    user.emailVerifyExpires = expiry
    await user.save()

    const verifyUrl = `${process.env.BASE_URL}/verify-email/${newUser._id}/${emailVerifyToken}`;

    // Send email
    const html = `
      <h3>Welcome to GigGlobe!</h3>
      <p>Click the link below to verify your email:</p>
      <a href="${verifyUrl}" target="_blank">Verify Email</a>
      <p>This link will expire in 1 hour.</p>
    `;

    await sendEmail(email, "Verify Your Email - GigGlobe", html);

    res.status(200).json({
      success: true,
      message: "Verification email resent successfully",
    })
  } catch (err) {
    console.error("Resend verification error:", err)
    res.status(500).json({ success: false, message: "Server error" })
  }
}